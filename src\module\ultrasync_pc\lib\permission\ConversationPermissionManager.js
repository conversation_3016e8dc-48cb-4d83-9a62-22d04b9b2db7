import BasePermissionManager from './BasePermissionManager.js';

/**
 * 会话权限管理器
 * 负责聊天会话内的角色权限控制（群主、管理员、普通成员等）
 */
class ConversationPermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.conversationRoles = new Map(); // 会话角色缓存 {conversationId: {userId: role}}
        this.conversationPermissions = new Map(); // 会话权限配置
        this.loadConversationPermissions();
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        this.loadConversationPermissions();
    }

    /**
     * 加载会话权限配置
     */
    loadConversationPermissions() {
        // 定义会话内的权限配置
        const conversationPermissions = {
            // 消息相关权限
            'message': {
                'send': { roles: ['owner', 'admin', 'member'] },
                'delete': { roles: ['owner', 'admin'], conditions: ['own_message'] },
                'withdraw': { roles: ['owner', 'admin', 'member'], conditions: ['own_message', 'time_limit'] },
                'pin': { roles: ['owner', 'admin'] },
                'forward': { roles: ['owner', 'admin', 'member'] }
            },
            // 成员管理权限
            'member': {
                'invite': { roles: ['owner', 'admin'] },
                'remove': { roles: ['owner', 'admin'] },
                'mute': { roles: ['owner', 'admin'] },
                'set_admin': { roles: ['owner'] },
                'view_list': { roles: ['owner', 'admin', 'member'] }
            },
            // 会话设置权限
            'conversation': {
                'edit_subject': { roles: ['owner', 'admin'] },
                'edit_announcement': { roles: ['owner', 'admin'] },
                'edit_settings': { roles: ['owner', 'admin'] },
                'delete': { roles: ['owner'] },
                'transfer_ownership': { roles: ['owner'] }
            },
            // 文件和资源权限
            'resource': {
                'upload': { roles: ['owner', 'admin', 'member'] },
                'download': { roles: ['owner', 'admin', 'member'] },
                'delete': { roles: ['owner', 'admin'], conditions: ['own_resource'] },
                'share': { roles: ['owner', 'admin', 'member'] }
            },
            // 直播会议权限
            'conference': {
                'start': { roles: ['owner', 'admin'] },
                'end': { roles: ['owner', 'admin'] },
                'mute_all': { roles: ['owner', 'admin'] },
                'control_camera': { roles: ['owner', 'admin'] },
                'screen_share': { roles: ['owner', 'admin', 'member'] }
            }
        };

        this.conversationPermissions.set('default', conversationPermissions);
    }

    /**
     * 设置用户在会话中的角色
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @param {string} role - 角色 (owner|admin|member|muted)
     */
    setUserConversationRole(conversationId, userId, role) {
        if (!this.conversationRoles.has(conversationId)) {
            this.conversationRoles.set(conversationId, new Map());
        }
        this.conversationRoles.get(conversationId).set(userId, role);

        // 清除相关缓存
        this.clearConversationCache(conversationId);
    }

    /**
     * 获取用户在会话中的角色
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {string} 角色
     */
    getUserConversationRole(conversationId, userId) {
        const conversationRoles = this.conversationRoles.get(conversationId);
        if (!conversationRoles) {
            return 'member'; // 默认为普通成员
        }
        return conversationRoles.get(userId) || 'member';
    }

    /**
     * 批量设置会话成员角色
     * @param {string} conversationId - 会话ID
     * @param {Object} memberRoles - 成员角色映射 {userId: role}
     */
    setConversationMemberRoles(conversationId, memberRoles) {
        const conversationRoleMap = new Map();
        for (const [userId, role] of Object.entries(memberRoles)) {
            conversationRoleMap.set(userId, role);
        }
        this.conversationRoles.set(conversationId, conversationRoleMap);
        this.clearConversationCache(conversationId);
    }

    /**
     * 检查会话权限
     * @param {string} permission - 权限标识 (格式: feature.action)
     * @param {Object} context - 上下文信息
     * @param {string} context.conversationId - 会话ID
     * @param {string} context.userId - 用户ID (可选，默认使用当前用户)
     * @param {string} context.targetUserId - 目标用户ID (用于某些操作)
     * @param {string} context.messageId - 消息ID (用于消息相关操作)
     * @param {string} context.resourceId - 资源ID (用于资源相关操作)
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission, context = {}) {
        if (!this.isInitialized()) {
            console.warn('ConversationPermissionManager not initialized');
            return false;
        }

        const { conversationId, userId = this.getUserId() } = context;

        if (!conversationId) {
            console.warn('ConversationPermissionManager: conversationId is required');
            return false;
        }

        // 解析权限标识
        const [feature, action] = permission.split('.');
        if (!feature || !action) {
            console.warn('ConversationPermissionManager: invalid permission format, expected "feature.action"');
            return false;
        }

        return this.checkConversationPermission(feature, action, context);
    }

    /**
     * 检查会话权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkConversationPermission(feature, action, context = {}) {
        const { conversationId, userId = this.getUserId() } = context;

        // 获取用户在会话中的角色
        const userRole = this.getUserConversationRole(conversationId, userId);

        // 被禁言的用户只能查看，不能操作
        if (userRole === 'muted') {
            const readOnlyActions = ['view_list', 'download'];
            return readOnlyActions.includes(action);
        }

        // 获取权限配置
        const permissionConfig = this.getPermissionConfig(feature, action);
        if (!permissionConfig) {
            return true; // 没有配置默认允许
        }

        // 检查角色权限
        if (!this.checkRolePermission(userRole, permissionConfig.roles)) {
            return false;
        }

        // 检查特殊条件
        if (permissionConfig.conditions) {
            return this.checkPermissionConditions(permissionConfig.conditions, context);
        }

        return true;
    }

    /**
     * 获取权限配置
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @returns {Object|null} 权限配置
     */
    getPermissionConfig(feature, action) {
        const permissions = this.conversationPermissions.get('default');
        return permissions?.[feature]?.[action] || null;
    }

    /**
     * 检查角色权限
     * @param {string} userRole - 用户角色
     * @param {Array} allowedRoles - 允许的角色列表
     * @returns {boolean} 是否有权限
     */
    checkRolePermission(userRole, allowedRoles = []) {
        return allowedRoles.includes(userRole);
    }

    /**
     * 检查权限条件
     * @param {Array} conditions - 条件列表
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否满足条件
     */
    checkPermissionConditions(conditions, context) {
        return conditions.every(condition => {
            switch (condition) {
            case 'own_message':
                return this.checkOwnMessage(context);
            case 'own_resource':
                return this.checkOwnResource(context);
            case 'time_limit':
                return this.checkTimeLimit(context);
            default:
                return true;
            }
        });
    }

    /**
     * 检查是否为自己的消息
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否为自己的消息
     */
    checkOwnMessage(context) {
        const { messageId, userId = this.getUserId() } = context;
        // 这里需要根据实际的消息数据结构来实现
        // 可以通过 messageId 查询消息的发送者
        return true; // 临时返回 true，需要根据实际情况实现
    }

    /**
     * 检查是否为自己的资源
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否为自己的资源
     */
    checkOwnResource(context) {
        const { resourceId, userId = this.getUserId() } = context;
        // 这里需要根据实际的资源数据结构来实现
        return true; // 临时返回 true，需要根据实际情况实现
    }

    /**
     * 检查时间限制（如撤回消息的时间限制）
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否在时间限制内
     */
    checkTimeLimit(context) {
        const { messageTime, timeLimit = 2 * 60 * 1000 } = context; // 默认2分钟
        if (!messageTime) {
            return true;
        }

        const now = Date.now();
        return (now - messageTime) <= timeLimit;
    }

    /**
     * 清除会话缓存
     * @param {string} conversationId - 会话ID
     */
    clearConversationCache(conversationId) {
        // 清除特定会话的权限缓存
        const cacheKeys = Array.from(this.permissions.keys()).filter(key =>
            key.startsWith(`conversation_${conversationId}_`)
        );
        cacheKeys.forEach(key => this.permissions.delete(key));
    }

    /**
     * 获取用户在会话中的权限列表
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {Object} 权限列表
     */
    getUserConversationPermissions(conversationId, userId = this.getUserId()) {
        const permissions = {};
        const allPermissions = this.conversationPermissions.get('default');

        for (const [feature, actions] of Object.entries(allPermissions)) {
            permissions[feature] = {};
            for (const [action] of Object.entries(actions)) {
                permissions[feature][action] = this.checkConversationPermission(
                    feature,
                    action,
                    { conversationId, userId }
                );
            }
        }

        return permissions;
    }

    /**
     * 检查是否为会话群主
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否为群主
     */
    isConversationOwner(conversationId, userId = this.getUserId()) {
        return this.getUserConversationRole(conversationId, userId) === 'owner';
    }

    /**
     * 检查是否为会话管理员（包括群主）
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否为管理员
     */
    isConversationAdmin(conversationId, userId = this.getUserId()) {
        const role = this.getUserConversationRole(conversationId, userId);
        return ['owner', 'admin'].includes(role);
    }

    /**
     * 销毁会话权限管理器
     */
    destroy() {
        this.conversationRoles.clear();
        this.conversationPermissions.clear();
        super.destroy();
    }
}

export default ConversationPermissionManager;
