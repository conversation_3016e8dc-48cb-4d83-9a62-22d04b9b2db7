/**
 * 测试权限管理器重构后的功能
 * 这个文件用于验证重构是否成功，不会提交到代码库
 */

// 模拟环境
global.window = {
    vm: {
        $store: {
            state: {
                globalParams: {
                    functionsStatus: {},
                    region: 'test'
                }
            }
        },
        $root: {
            eventBus: {
                $emit: () => {}
            }
        },
        $emit: () => {}
    },
    dispatchEvent: () => {},
    CustomEvent: function(type, options) {
        this.type = type;
        this.detail = options?.detail;
    }
};

// 模拟 USER_ROLE 常量
const USER_ROLE = {
    ADMIN: 3,
    SUPER_ADMIN: 4,
    DIRECTOR: 2
};

// 导入权限管理器
import permissionManager, { PermissionManager } from './src/module/ultrasync_pc/lib/permission/PermissionManager.js';

async function testPermissionManager() {
    console.log('开始测试权限管理器重构...');

    try {
        // 测试单例模式
        const manager1 = PermissionManager.getInstance();
        const manager2 = PermissionManager.getInstance();
        const manager3 = permissionManager; // 默认导出的实例
        console.log('✓ 单例模式测试通过:', manager1 === manager2 && manager1 === manager3);

        // 测试继承的基础方法
        const testUserInfo = {
            uid: 'test123',
            role: USER_ROLE.ADMIN,
            type: 1
        };

        // 使用默认导出的实例进行测试
        const manager = permissionManager;

        // 测试初始化
        await manager.initialize(testUserInfo, { test: true });
        console.log('✓ 初始化测试通过');

        // 测试继承的方法
        console.log('✓ getUserRole():', manager.getUserRole());
        console.log('✓ getUserId():', manager.getUserId());
        console.log('✓ isAdmin():', manager.isAdmin());
        console.log('✓ isSuperAdmin():', manager.isSuperAdmin());

        // 测试特有方法
        console.log('✓ isDirector():', manager.isDirector());
        console.log('✓ isInitialized():', manager.isInitialized());

        // 测试权限检查
        console.log('✓ checkPermission() 字符串模式:', manager.checkPermission('test_permission'));

        // 测试缓存清理
        manager.clearCache();
        console.log('✓ clearCache() 测试通过');

        // 测试用户信息更新
        manager.updateUserInfo({ role: USER_ROLE.DIRECTOR });
        console.log('✓ updateUserInfo() 测试通过');
        console.log('✓ 更新后 getUserRole():', manager.getUserRole());
        console.log('✓ 更新后 isDirector():', manager.isDirector());
        console.log('✓ 更新后 isAdmin():', manager.isAdmin());

        console.log('\n🎉 所有测试通过！重构成功！');

    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 运行测试
testPermissionManager();
