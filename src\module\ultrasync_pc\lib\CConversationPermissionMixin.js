import permissionManager from './permission/index.js';

/**
 * CConversation 权限管理混入
 * 为 CConversation 类添加权限管理功能
 */
const CConversationPermissionMixin = {
    /**
     * 初始化会话权限
     * @param {Object} conversationData - 会话数据
     */
    initConversationPermissions(conversationData) {
        if (!conversationData || !conversationData.attendeeList) {
            console.warn('CConversation: Invalid conversation data for permission initialization');
            return;
        }

        // 解析会话成员角色
        const memberRoles = this.parseConversationMemberRoles(conversationData);

        // 设置会话成员角色
        permissionManager.setConversationMemberRoles(this.cid, memberRoles);

        console.log(`[CConversation] Initialized permissions for conversation ${this.cid}`, memberRoles);
    },

    /**
     * 解析会话成员角色
     * @param {Object} conversationData - 会话数据
     * @returns {Object} 成员角色映射 {userId: role}
     */
    parseConversationMemberRoles(conversationData) {
        const memberRoles = {};
        const { attendeeList, creator_id } = conversationData;

        // 遍历参与者列表
        for (const attendeeKey in attendeeList) {
            const attendee = attendeeList[attendeeKey];
            if (!attendee || !attendee.uid) {
                continue;
            }

            const userId = attendee.uid;

            // 确定用户角色
            let role = 'member'; // 默认为普通成员

            if (userId === creator_id) {
                role = 'owner'; // 群主
            } else if (attendee.is_admin || attendee.role === 'admin') {
                role = 'admin'; // 管理员
            } else if (attendee.is_muted || attendee.mute_status) {
                role = 'muted'; // 被禁言
            }

            memberRoles[userId] = role;
        }

        return memberRoles;
    },

    /**
     * 更新用户在会话中的角色
     * @param {string} userId - 用户ID
     * @param {string} role - 新角色
     */
    updateUserConversationRole(userId, role) {
        permissionManager.setUserConversationRole(this.cid, userId, role);
        console.log(`[CConversation] Updated role for user ${userId} in conversation ${this.cid}: ${role}`);
    },

    /**
     * 检查当前用户是否有权限执行某个操作
     * @param {string} permission - 权限标识 (格式: feature.action)
     * @param {Object} context - 额外的上下文信息
     * @returns {boolean} 是否有权限
     */
    checkConversationPermission(permission, context = {}) {
        const permissionContext = {
            conversationId: this.cid,
            userId: this.uid,
            ...context
        };

        return permissionManager.checkConversationPermission(permission, permissionContext);
    },

    /**
     * 检查指定用户是否有权限执行某个操作
     * @param {string} userId - 用户ID
     * @param {string} permission - 权限标识
     * @param {Object} context - 额外的上下文信息
     * @returns {boolean} 是否有权限
     */
    checkUserConversationPermission(userId, permission, context = {}) {
        const permissionContext = {
            conversationId: this.cid,
            userId: userId,
            ...context
        };

        return permissionManager.checkConversationPermission(permission, permissionContext);
    },

    /**
     * 获取当前用户在会话中的角色
     * @returns {string} 用户角色
     */
    getCurrentUserConversationRole() {
        return permissionManager.getUserConversationRole(this.cid, this.uid);
    },

    /**
     * 检查当前用户是否为群主
     * @returns {boolean} 是否为群主
     */
    isCurrentUserConversationOwner() {
        return permissionManager.isConversationOwner(this.cid, this.uid);
    },

    /**
     * 检查当前用户是否为管理员（包括群主）
     * @returns {boolean} 是否为管理员
     */
    isCurrentUserConversationAdmin() {
        return permissionManager.isConversationAdmin(this.cid, this.uid);
    },

    /**
     * 获取当前用户在会话中的所有权限
     * @returns {Object} 权限列表
     */
    getCurrentUserConversationPermissions() {
        return permissionManager.getUserConversationPermissions(this.cid, this.uid);
    },

    /**
     * 处理会话成员变更事件
     * @param {Object} eventData - 事件数据
     */
    handleConversationMemberChange(eventData) {
        const { type, userId, role, attendeeList } = eventData;

        switch (type) {
        case 'add':
            // 新增成员
            if (userId && role) {
                this.updateUserConversationRole(userId, role);
            }
            break;

        case 'remove':
            // 移除成员 - 可以选择清除角色或保留历史记录
            // permissionManager.setUserConversationRole(this.cid, userId, null);
            break;

        case 'role_change':
            // 角色变更
            if (userId && role) {
                this.updateUserConversationRole(userId, role);
            }
            break;

        case 'batch_update':
            // 批量更新（如重新加载会话数据）
            if (attendeeList) {
                const memberRoles = this.parseConversationMemberRoles({
                    attendeeList,
                    creator_id: this.creator_id
                });
                permissionManager.setConversationMemberRoles(this.cid, memberRoles);
            }
            break;

        default:
            console.warn(`[CConversation] Unknown member change type: ${type}`);
        }
    },

    /**
     * 权限检查的便捷方法
     */
    conversationPermissions: {
        // 消息权限
        canSendMessage() {
            return this.checkConversationPermission('message.send');
        },

        canDeleteMessage(messageId, messageUserId) {
            return this.checkConversationPermission('message.delete', {
                messageId,
                targetUserId: messageUserId
            });
        },

        canWithdrawMessage(messageId, messageTime) {
            return this.checkConversationPermission('message.withdraw', {
                messageId,
                messageTime
            });
        },

        canPinMessage() {
            return this.checkConversationPermission('message.pin');
        },

        // 成员管理权限
        canInviteMembers() {
            return this.checkConversationPermission('member.invite');
        },

        canRemoveMembers() {
            return this.checkConversationPermission('member.remove');
        },

        canMuteMembers() {
            return this.checkConversationPermission('member.mute');
        },

        canSetAdmin() {
            return this.checkConversationPermission('member.set_admin');
        },

        // 会话设置权限
        canEditSubject() {
            return this.checkConversationPermission('conversation.edit_subject');
        },

        canEditAnnouncement() {
            return this.checkConversationPermission('conversation.edit_announcement');
        },

        canEditSettings() {
            return this.checkConversationPermission('conversation.edit_settings');
        },

        canDeleteConversation() {
            return this.checkConversationPermission('conversation.delete');
        },

        // 直播会议权限
        canStartConference() {
            return this.checkConversationPermission('conference.start');
        },

        canEndConference() {
            return this.checkConversationPermission('conference.end');
        },

        canMuteAll() {
            return this.checkConversationPermission('conference.mute_all');
        },

        canControlCamera() {
            return this.checkConversationPermission('conference.control_camera');
        },

        // 资源权限
        canUploadResource() {
            return this.checkConversationPermission('resource.upload');
        },

        canDeleteResource(resourceId, resourceUserId) {
            return this.checkConversationPermission('resource.delete', {
                resourceId,
                targetUserId: resourceUserId
            });
        }
    }
};

export default CConversationPermissionMixin;
